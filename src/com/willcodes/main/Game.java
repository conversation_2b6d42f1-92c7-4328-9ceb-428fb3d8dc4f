package com.willcodes.main;

import java.awt.*;
import java.awt.event.KeyListener;
import java.awt.image.BufferStrategy;
import java.util.Random;

public class Game extends Canvas implements Runnable {

    public static final int WIDTH = 640, HEIGHT = WIDTH / 12 * 9;
    private Thread thread;
    private boolean running = false;
    public static int frames;

    private final Handler handler;

    public Game() {
        // 加载游戏图片资源
        ImageLoader.loadImages();
        
        HUD hud = new HUD();
        handler = new Handler();
        this.addKeyListener(new KeyInput(handler));
        new Window(WIDTH, HEIGHT, "Wills Game", this);
        Random r = new Random();

        // 调整玩家初始位置到屏幕中心
        handler.addObject(new Player(WIDTH/2-24, HEIGHT/2-24, ID.Player, handler));
        
        // 随机生成敌人，但确保它们不会太靠近玩家的初始位置
        for (int i = 0; i < 6; i++) {
            int enemyX, enemyY;
            // 尝试生成不在玩家附近的敌人位置
            do {
                enemyX = r.nextInt(WIDTH - 40);
                enemyY = r.nextInt(HEIGHT - 60);
            } while (Math.abs(enemyX - WIDTH/2) < 100 && Math.abs(enemyY - HEIGHT/2) < 100);
            
            handler.addObject(new BasicEnemy(enemyX, enemyY, ID.BasicEnemy, handler));
        }
    }

    public synchronized void start() {
        thread = new Thread(this);
        thread.start();
        running = true;
    }

    public synchronized void stop() {
        try {
            thread.join();
            running = false;
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    public void run() {
        this.requestFocus();
        long lastTime = System.nanoTime();
        double amountOfTicks = 70.0;
        double ns = 1000000000 / amountOfTicks;
        double delta = 0;
        long timer = System.currentTimeMillis();
        frames = 0;
        while (running) {
            long now = System.nanoTime();
            delta += (now - lastTime) / ns;
            lastTime = now;
            while (delta >= 1) {
                tick();
                delta--;
            }
            if (running) {
                render();
            }
            frames++;
            if (System.currentTimeMillis() - timer > 1000) {
                timer += 1000;
                frames = 0;
            }
        }
        stop();
    }
    private void tick() {
        handler.tick();
        HUD.tick();
    }
    private void render() {
        BufferStrategy bs = this.getBufferStrategy();
        if (bs == null) {
            this.createBufferStrategy(3);
            return;
        }
        Graphics g = bs.getDrawGraphics();
        g.setColor(Color.BLACK);
        g.fillRect(0, 0, WIDTH, HEIGHT);
        handler.render(g);
        HUD.render(g);
        g.dispose();
        bs.show();
    }

    public static int clamp(int var, int min, int max) {
        if (var >= max) {
            return var = max;
        } else if (var <= min) {
            return var = min;
        }
        else
            return var;
    }

    public static void main(String[] args) {
        new Game();
    }

}
