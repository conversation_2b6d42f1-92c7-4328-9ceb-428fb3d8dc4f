package com.willcodes.main;

import java.awt.*;
import java.awt.image.BufferStrategy;
import java.util.Random;

public class Game extends Canvas implements Runnable {

    public static final int WIDTH = 640, HEIGHT = WIDTH / 12 * 9;
    private Thread thread;
    private boolean running = false;
    public static int frames;

    private final Handler handler;
    public GameState gameState = GameState.GAME;

    private Random r = new Random();
    private int spawnTimer = 0;
    private int powerUpTimer = 0;

    public Game() {
        // 加载游戏图片资源
        ImageLoader.loadImages();

        // 加载音频资源
        AudioPlayer.loadAudio();

        handler = new Handler();
        this.addKeyListener(new KeyInput(handler));
        new Window(WIDTH, HEIGHT, "Wills Enhanced Game", this);

        initGame();

        // 开始播放背景音乐
        AudioPlayer.loop("background");
    }

    public void initGame() {
        // 清空所有对象
        handler.objects.clear();

        // 重置游戏状态
        HUD.HEALTH = 100;
        HUD.score = 0;
        HUD.level = 1;
        HUD.enemiesKilled = 0;

        // 调整玩家初始位置到屏幕中心
        handler.addObject(new Player(WIDTH/2-24, HEIGHT/2-24, ID.Player, handler));

        // 生成初始敌人
        spawnEnemies();

        // 生成初始道具
        spawnPowerUp();
    }

    private void spawnEnemies() {
        int enemyCount = 3 + HUD.level; // 根据等级增加敌人数量

        for (int i = 0; i < enemyCount; i++) {
            int enemyX, enemyY;
            // 尝试生成不在玩家附近的敌人位置
            do {
                enemyX = r.nextInt(WIDTH - 40);
                enemyY = r.nextInt(HEIGHT - 60);
            } while (Math.abs(enemyX - WIDTH/2) < 100 && Math.abs(enemyY - HEIGHT/2) < 100);

            // 根据等级和随机数生成不同类型的敌人
            int enemyType = r.nextInt(100);
            if (enemyType < 60) {
                handler.addObject(new BasicEnemy(enemyX, enemyY, ID.BasicEnemy, handler));
            } else if (enemyType < 85) {
                handler.addObject(new SmartEnemy(enemyX, enemyY, ID.SmartEnemy, handler));
            } else {
                handler.addObject(new ShooterEnemy(enemyX, enemyY, ID.ShooterEnemy, handler));
            }
        }
    }

    private void spawnPowerUp() {
        if (r.nextInt(100) < 30) { // 30% 概率生成道具
            int x = r.nextInt(WIDTH - 50) + 25;
            int y = r.nextInt(HEIGHT - 100) + 50;

            PowerUp.PowerUpType[] types = PowerUp.PowerUpType.values();
            PowerUp.PowerUpType type = types[r.nextInt(types.length)];

            handler.addObject(new PowerUp(x, y, ID.PowerUp, type, handler));
        }
    }

    public synchronized void start() {
        thread = new Thread(this);
        thread.start();
        running = true;
    }

    public synchronized void stop() {
        try {
            thread.join();
            running = false;
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    public void run() {
        this.requestFocus();
        long lastTime = System.nanoTime();
        double amountOfTicks = 70.0;
        double ns = 1000000000 / amountOfTicks;
        double delta = 0;
        long timer = System.currentTimeMillis();
        frames = 0;
        while (running) {
            long now = System.nanoTime();
            delta += (now - lastTime) / ns;
            lastTime = now;
            while (delta >= 1) {
                tick();
                delta--;
            }
            if (running) {
                render();
            }
            frames++;
            if (System.currentTimeMillis() - timer > 1000) {
                timer += 1000;
                frames = 0;
            }
        }
        stop();
    }
    private void tick() {
        handler.tick();
        HUD.tick();

        // 动态生成敌人
        spawnTimer++;
        if (spawnTimer >= 600) { // 每10秒检查一次
            int currentEnemies = countEnemies();
            int maxEnemies = 5 + HUD.level * 2;

            if (currentEnemies < maxEnemies) {
                spawnSingleEnemy();
            }
            spawnTimer = 0;
        }

        // 动态生成道具
        powerUpTimer++;
        if (powerUpTimer >= 900) { // 每15秒生成道具
            spawnPowerUp();
            powerUpTimer = 0;
        }
    }

    private int countEnemies() {
        int count = 0;
        for (GameObject obj : handler.objects) {
            if (obj.getId() == ID.BasicEnemy ||
                obj.getId() == ID.SmartEnemy ||
                obj.getId() == ID.ShooterEnemy ||
                obj.getId() == ID.BossEnemy) {
                count++;
            }
        }
        return count;
    }

    private void spawnSingleEnemy() {
        int enemyX, enemyY;
        // 在屏幕边缘生成敌人
        if (r.nextBoolean()) {
            enemyX = r.nextBoolean() ? 0 : WIDTH - 40;
            enemyY = r.nextInt(HEIGHT - 60);
        } else {
            enemyX = r.nextInt(WIDTH - 40);
            enemyY = r.nextBoolean() ? 0 : HEIGHT - 60;
        }

        // 根据等级生成不同类型的敌人
        int enemyType = r.nextInt(100);
        if (enemyType < 50) {
            handler.addObject(new BasicEnemy(enemyX, enemyY, ID.BasicEnemy, handler));
        } else if (enemyType < 80) {
            handler.addObject(new SmartEnemy(enemyX, enemyY, ID.SmartEnemy, handler));
        } else {
            handler.addObject(new ShooterEnemy(enemyX, enemyY, ID.ShooterEnemy, handler));
        }
    }
    private void render() {
        BufferStrategy bs = this.getBufferStrategy();
        if (bs == null) {
            this.createBufferStrategy(3);
            return;
        }
        Graphics g = bs.getDrawGraphics();
        Graphics2D g2d = (Graphics2D) g;
        g2d.setRenderingHint(RenderingHints.KEY_ANTIALIASING, RenderingHints.VALUE_ANTIALIAS_ON);

        // 绘制动态星空背景
        renderStarField(g);

        handler.render(g);
        HUD.render(g);
        g.dispose();
        bs.show();
    }

    private void renderStarField(Graphics g) {
        // 绘制渐变背景
        Graphics2D g2d = (Graphics2D) g;
        GradientPaint gradient = new GradientPaint(0, 0, new Color(10, 10, 30),
                                                  0, HEIGHT, new Color(30, 10, 50));
        g2d.setPaint(gradient);
        g2d.fillRect(0, 0, WIDTH, HEIGHT);

        // 绘制动态星星
        g.setColor(Color.WHITE);
        for (int i = 0; i < 100; i++) {
            int x = (int) ((i * 37 + frames * 0.5) % WIDTH);
            int y = (int) ((i * 73 + frames * 0.3) % HEIGHT);
            int brightness = (int) (Math.sin(frames * 0.01 + i) * 50 + 150);
            brightness = Math.max(0, Math.min(255, brightness));
            g.setColor(new Color(brightness, brightness, brightness));
            g.fillOval(x, y, 1, 1);
        }

        // 绘制更大的星星
        for (int i = 0; i < 20; i++) {
            int x = (int) ((i * 127 + frames * 0.2) % WIDTH);
            int y = (int) ((i * 193 + frames * 0.1) % HEIGHT);
            int brightness = (int) (Math.sin(frames * 0.02 + i) * 100 + 155);
            brightness = Math.max(0, Math.min(255, brightness));
            int blue = Math.max(0, Math.min(255, brightness + 50));
            g.setColor(new Color(brightness, brightness, blue));
            g.fillOval(x, y, 2, 2);
        }
    }

    public static int clamp(int var, int min, int max) {
        if (var >= max) {
            return var = max;
        } else if (var <= min) {
            return var = min;
        }
        else
            return var;
    }

    public static void main(String[] args) {
        new Game();
    }

}
