package com.willcodes.main;

import java.awt.*;
import java.awt.event.MouseAdapter;
import java.awt.event.MouseEvent;

public class <PERSON>u extends MouseAdapter {
    private Game game;
    private Handler handler;
    private Font titleFont;
    private Font menuFont;
    
    public Menu(Game game, Handler handler) {
        this.game = game;
        this.handler = handler;
        titleFont = new Font("Arial", Font.BOLD, 50);
        menuFont = new Font("Arial", Font.BOLD, 30);
    }
    
    public void mousePressed(MouseEvent e) {
        int mx = e.getX();
        int my = e.getY();
        
        if (game.gameState == GameState.MENU) {
            // 开始游戏按钮
            if (mouseOver(mx, my, 210, 150, 200, 64)) {
                game.gameState = GameState.GAME;
                handler.clearObjects();
                game.initGame();
                AudioPlayer.play("powerup");
            }
            
            // 帮助按钮
            if (mouseOver(mx, my, 210, 250, 200, 64)) {
                game.gameState = GameState.HELP;
                AudioPlayer.play("powerup");
            }
            
            // 退出按钮
            if (mouseOver(mx, my, 210, 350, 200, 64)) {
                System.exit(0);
            }
        } else if (game.gameState == GameState.HELP) {
            // 返回按钮
            if (mouseOver(mx, my, 210, 350, 200, 64)) {
                game.gameState = GameState.MENU;
                AudioPlayer.play("powerup");
            }
        } else if (game.gameState == GameState.GAMEOVER) {
            // 返回菜单按钮
            if (mouseOver(mx, my, 210, 350, 200, 64)) {
                game.gameState = GameState.MENU;
                AudioPlayer.play("powerup");
            }
        }
    }
    
    private boolean mouseOver(int mx, int my, int x, int y, int width, int height) {
        return mx > x && mx < x + width && my > y && my < y + height;
    }
    
    public void tick() {
        
    }
    
    public void render(Graphics g) {
        if (game.gameState == GameState.MENU) {
            g.setColor(Color.BLACK);
            g.fillRect(0, 0, Game.WIDTH, Game.HEIGHT);
            
            g.setFont(titleFont);
            g.setColor(Color.WHITE);
            g.drawString("Wills Game", 180, 70);
            
            g.setFont(menuFont);
            g.setColor(Color.WHITE);
            g.drawRect(210, 150, 200, 64);
            g.drawString("开始游戏", 240, 190);
            
            g.drawRect(210, 250, 200, 64);
            g.drawString("帮助", 270, 290);
            
            g.drawRect(210, 350, 200, 64);
            g.drawString("退出", 270, 390);
        } else if (game.gameState == GameState.HELP) {
            g.setColor(Color.BLACK);
            g.fillRect(0, 0, Game.WIDTH, Game.HEIGHT);
            
            g.setFont(titleFont);
            g.setColor(Color.WHITE);
            g.drawString("帮助", 250, 70);
            
            g.setFont(new Font("Arial", Font.PLAIN, 20));
            g.drawString("使用WASD键移动角色", 180, 150);
            g.drawString("躲避红色敌人", 180, 180);
            g.drawString("收集绿色能量提高分数", 180, 210);
            g.drawString("按P键暂停游戏", 180, 240);
            g.drawString("按M键开关音效", 180, 270);
            
            g.setFont(menuFont);
            g.drawRect(210, 350, 200, 64);
            g.drawString("返回", 270, 390);
        } else if (game.gameState == GameState.GAMEOVER) {
            g.setColor(new Color(0, 0, 0, 200));
            g.fillRect(0, 0, Game.WIDTH, Game.HEIGHT);
            
            g.setFont(titleFont);
            g.setColor(Color.RED);
            g.drawString("游戏结束", 180, 70);
            
            g.setFont(new Font("Arial", Font.BOLD, 30));
            g.setColor(Color.WHITE);
            g.drawString("得分: " + HUD.score, 250, 200);
            
            g.setFont(menuFont);
            g.drawRect(210, 350, 200, 64);
            g.drawString("返回菜单", 240, 390);
        }
    }
}