# Java 小游戏

这是一个基于Java AWT开发的简单2D游戏，玩家可以控制一个角色躲避敌人。

## 游戏说明

游戏中，玩家控制一个方块角色在屏幕上移动，同时需要避开随机移动的敌人。

### 游戏特点

- 2D图形界面
- 键盘控制的玩家角色
- 随机移动的敌人
- 简单的碰撞检测
- 游戏状态显示(HUD)

## 如何运行游戏

### 环境要求

- Java运行环境(JRE) 8或更高版本
- Java开发工具包(JDK) 8或更高版本(如需编译)

### 运行方法

#### 方法1：直接运行JAR文件(推荐)

项目已经打包成可执行JAR文件，可以直接运行:

1. 双击 `out\artifacts\JavaTut_jar\JavaTut.jar` 文件直接运行
2. 或者通过命令行运行:
   ```bash
   java -jar out\artifacts\JavaTut_jar\JavaTut.jar
   ```

#### 方法2：使用IDE

1. 使用IntelliJ IDEA、Eclipse或其他Java IDE打开项目文件夹
2. 找到`src/com/willcodes/main/Game.java`文件
3. 右键点击并选择"运行"或"执行"选项

#### 方法3：命令行编译运行

1. 打开命令行窗口(Windows中的PowerShell或CMD，MacOS/Linux中的Terminal)
2. 导航到项目根目录
3. 编译代码:
   ```bash
   javac -d out src/com/willcodes/main/*.java
   ```
4. 运行游戏:
   ```bash
   java -cp out com.willcodes.main.Game
   ```

#### 方法4：直接运行已编译文件

如果项目中已包含编译好的类文件，可以直接运行:
```bash
java -cp out com.willcodes.main.Game
```

## 游戏控制

- 使用键盘方向键(上、下、左、右)控制玩家角色移动
- 躲避红色的敌人方块
- 尽可能长时间生存

## 自定义游戏角色图片

游戏支持使用自定义二次元图片作为玩家和敌人角色：

1. 准备两张PNG格式的图片：
   - `player.png` - 作为玩家角色的图片（建议尺寸32x32像素或更大）
   - `enemy.png` - 作为敌人角色的图片（建议尺寸16x16像素或更大）

2. 将这两张图片放入 `src/images/` 目录下

3. 重新编译并运行游戏即可看到自定义的角色图片

注意：
- 建议使用透明背景的PNG图片以获得最佳效果
- 图片将自动缩放到合适大小显示
- 如果没有找到图片文件，游戏将回退使用原始的彩色方块

## 项目结构

- `src/com/willcodes/main/`: 包含游戏的所有源代码
  - `Game.java`: 游戏主类，包含游戏循环和主入口
  - `Player.java`: 玩家角色类
  - `BasicEnemy.java`: 基本敌人类
  - `Handler.java`: 管理游戏对象
  - `Window.java`: 创建游戏窗口
  - `KeyInput.java`: 处理键盘输入
  - `ImageLoader.java`: 图片资源加载类
  - 其他支持类...
- `src/images/`: 存放游戏角色图片
- `out/artifacts/JavaTut_jar/JavaTut.jar`: 打包好的可执行JAR文件

## 开发者信息

该游戏由willcodes开发。

## 许可证

请参阅项目中的LICENSE文件(如有)。
